config:
  target: 'http://localhost:4000/api'
  phases:
    - duration: 60
      arrivalRate: 10
#   processor: './artillery-token.js'
scenarios:
  - name: 'Login and test authenticated endpoints'
    flow:
      # 1. Login to get Bear<PERSON> token
      - post:
          url: '/login'
          json:
            email: '<EMAIL>'
            password: 'Pass@123'
          capture:
            - json: '$.accessToken'
              as: 'authToken'
      # 2. Authenticated requests (token will be set by processor)
      - get:
          url: '/profile'
          headers:
            Authorization: 'Bear<PERSON> {{ authToken }}'
      - get:
          url: '/meal_records'
          headers:
            Authorization: 'Bearer {{ authToken }}'
      - post:
          url: '/meal_records'
          headers:
            Authorization: 'Bearer {{ authToken }}'
          json:
            # Add a valid payload for meal record creation here
            sampleField: 'sampleValue'
      - put:
          url: '/profile'
          headers:
            Authorization: 'Bearer {{ authToken }}'
          json:
            # Add a valid payload for profile update here
            sampleField: 'sampleValue'
      - get:
          url: '/weight_records'
          headers:
            Authorization: 'Bear<PERSON> {{ authToken }}'
      - get:
          url: '/status'
          headers:
            Authorization: 'Bearer {{ authToken }}'
      # Notification Token Endpoints
      - post:
          url: '/notification_token'
          headers:
            Authorization: 'Bearer {{ authToken }}'
          json:
            deviceId: 'device123'
            notificationToken: 'notifToken123'
            osType: 'android'
            deviceType: 'mobile'
      - put:
          url: '/notification_token'
          headers:
            Authorization: 'Bearer {{ authToken }}'
          json:
            deviceId: 'device123'
            isNotificationActive: true
      - get:
          url: '/notification_token/device123'
          headers:
            Authorization: 'Bearer {{ authToken }}'
      # Add more authenticated endpoints as needed
  - name: 'Public endpoints'
    flow:
      - get:
          url: '/health'
      # Add more public endpoints as needed
# Note: For a full test, expand the scenario with all discovered endpoints and provide valid payloads for POST/PUT requests.
# The processor (artillery-token.js) is optional if you want to manage tokens globally or add custom logic.
