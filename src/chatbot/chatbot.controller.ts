import {
  Body,
  Controller,
  Get,
  Param,
  Post,
  Query,
  UseGuards,
  Headers,
} from '@nestjs/common';
import { ChatbotService } from './chatbot.service';
import {
  AskChatBotResDTO,
  ChatBotReqDTO,
  GetAllSessionMessagesResDTO,
} from './dto';
import { AuthGuard } from 'src/middlewares';
import { ApiBearerAuth, ApiResponse, ApiTags } from '@nestjs/swagger';
import { ErrorResponse } from 'src/utils/responses';
import { GetAllSessionMessagesQueryInterface } from './interfaces';

@ApiTags('Chatbot')
@ApiBearerAuth()
@UseGuards(AuthGuard)
@Controller('chat')
export class ChatbotController {
  constructor(private readonly chatbotService: ChatbotService) {}

  @ApiResponse({
    status: 200,
    description: 'Successfully recieved the chat bot response.',
    type: AskChatBotResDTO,
  })
  @ApiResponse({
    status: 404,
    description: 'Error Response',
    type: ErrorResponse,
  })
  @Post()
  async AskChatBot(
    @Body() reqBody: ChatBotReqDTO,
    @Headers('authorization') token: string,
  ) {
    console.log('ChatBot Request Body:', reqBody);
    return this.chatbotService.askChatBot(reqBody, token);
  }

  @ApiResponse({
    status: 200,
    description: 'Successfully recieved session messages.',
    type: GetAllSessionMessagesResDTO,
  })
  @ApiResponse({
    status: 404,
    description: 'Error Response',
    type: ErrorResponse,
  })
  @Get('/:sessionId')
  async GetAllSessionMessages(
    @Param('sessionId') sessionId: string,
    @Query() queryFilters: GetAllSessionMessagesQueryInterface,
  ) {
    return this.chatbotService.getAllSessionMessages(sessionId, queryFilters);
  }
}
