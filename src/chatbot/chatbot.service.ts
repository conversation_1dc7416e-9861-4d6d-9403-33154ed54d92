import {
  BadRequestException,
  HttpStatus,
  Injectable,
  Logger,
  InternalServerErrorException,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, modelNames } from 'mongoose';
import { ChatOpenAI, OpenAI } from '@langchain/openai';
import { MESSAGE_ROLE, Chat_Message, Chat_Session } from 'src/models/chat';
import {
  AskChatBotResDTO,
  ChatBotReqDTO,
  ChatBotResponseDTO,
  ChatMessageDTO,
  GetAllSessionMessagesResDTO,
  UserDTO,
} from './dto';
import { Types } from 'mongoose';
import { MicroserviceHttpClientService } from 'src/common/services/MicroserviceHttpClient.service';
import { ConfigService } from '@nestjs/config';
import { GetAllSessionMessagesQueryInterface } from './interfaces';
import { UtilsService } from 'src/common/services';
import { HealthDataService } from './health-data/health-data.service';
type SessionCache = {
  helpData: any[];
  faqData: any[];
};
import {
  SUPPORTED_CATEGORIES,
  CLASSIFICATION_PROMPT,
  ChatbotCategory,
  CHATBOT_PROMPT_TEMPLATE,
} from 'src/chatbot/prompts';
import { ChatbotUtilsService } from './chatbot.utils.service';
import { HumanMessage, SystemMessage } from '@langchain/core/messages';
import { RedisService } from 'src/redis/redis.service';
import { HealthDeviceService } from './health-device/health-device.service';
import { BuildMessagePromptService } from 'src/chatbot/prompts';
import { ChatbotRedisService } from 'src/chatbot/cache-service/chatbot.redis.service';
import { console } from 'inspector';

@Injectable()
export class ChatbotService {
  //   private llm: ChatOpenAI;
  private sessionStore = new Map<string, SessionCache>();
  private readonly API_KEY: string;
  private readonly HEALTH_MICROSERVICE_URL: string;
  private readonly PROMPT_TEMPLATE: string;
  private stripMarkdownBold(text: string): string {
    return text.replace(/\*\*(.*?)\*\*/g, '$1');
  }

  constructor(
    @InjectModel(Chat_Message.name) private messageModel: Model<Chat_Message>,
    @InjectModel(Chat_Session.name) private sessionModel: Model<Chat_Session>,

    private readonly logger: Logger,
    private readonly openApiKey: string,
    private readonly configService: ConfigService,
    private readonly utilsService: UtilsService,
    private readonly healthDataService: HealthDataService,
    private readonly buildMessagePromptService: BuildMessagePromptService,
    private readonly redisCacheService: RedisService,
    private readonly healthDeviceService: HealthDeviceService,
    private readonly chatbotUtilsService: ChatbotUtilsService,
    private readonly chatbotRedisService: ChatbotRedisService,
  ) {
    this.API_KEY = `${this.configService.get<string>('HEALTH_MICROSERVICE_API_KEY')}`;

    this.HEALTH_MICROSERVICE_URL = `${this.configService.get<string>('HEALTH_MICROSERVICE_URL')}/health`;
    this.openApiKey =
      '********************************************************************************************************************************************************************';
    // const logger = new Logger('ChatbotService');

    //     this.llm = new ChatOpenAI({
    //       model: 'gpt-4o-mini',

    //       temperature: 0,
    //       apiKey: process.env.OPENAI_API_KEY,
    //     });
  }

  async askChatBot(
    reqBody: ChatBotReqDTO,
    token: string,
  ): Promise<AskChatBotResDTO> {
    try {
      const { sessionId, user, attachment } = reqBody;
      //   console.log('ChatBot Request Body:', attachment);
      const originalQuestion = reqBody.question;
      console.log('this.llm', user);
      //   const question = this.resolveRelativeDates(originalQuestion);
      const question =
        this.chatbotUtilsService.resolveRelativeDates(originalQuestion);

      let existing_chat_session: Chat_Session | null = null;

      const [healthApiRes, fallbackHealthDataRes] = await Promise.all([
        this.healthDataService.findUserhealthData(user),
        this.healthDataService.fetchEncryptedHealthData(user._id),
      ]);

      const decryptedHealthRes = await this.healthDeviceService.getHealthData(
        user._id,
      );
      const usermenuData = decryptedHealthRes?.data ?? null;

      //   console.log('decryptedMicroserviceData', usermenuData);

      const healthMicroserviceData = healthApiRes?.data ?? null;
      const healthDump = fallbackHealthDataRes?.data ?? null;

      // console.log('Health Microservice Data:', healthMicroserviceData);
      // console.log('Health Dump Data:', healthDump);

      const mergedHealthContext = {
        microservice: healthMicroserviceData,
        dump: healthDump,
      };
      let isNewSession = false;

      if (!sessionId || !Types.ObjectId.isValid(sessionId)) {
        isNewSession = true;
      } else {
        existing_chat_session = await this.sessionModel.findById(sessionId);
        if (!existing_chat_session) isNewSession = true;
      }

      if (isNewSession) {
        const healthData =
          await this.healthDataService.findUserhealthData(user);
        existing_chat_session = await this.sessionModel.create({
          userId: user._id,
          healthDataId: healthData._id,
        });
        console.log('Creating new session. Clearing Redis cache...');
        // Always clear old cached messages when a new session is started
        await this.chatbotRedisService.clearAllUserSessionCaches(user._id);
      }

      const messages = await this.messageModel
        .find({ sessionId: existing_chat_session?._id })
        .sort({ createdAt: 1 })
        .limit(40)
        .lean();

      const chatHistory = messages.map((msg) => ({
        role: msg.role,
        content: msg.content,
      }));

      // Select the prompt template based on the question
      const category = await this.classifyPromptUsingLLM(question);

      //   console.log('Classified Category:', category);
      const selectedPromptTemplate = CHATBOT_PROMPT_TEMPLATE[category];
      // console.log('Selected Prompt Template:', selectedPromptTemplate);
      const attachmentPrompt =
        reqBody.attachment?.type === 'image'
          ? `The user has also attached an image. If relevant, analyze the image and incorporate its content in your answer.`
          : '';

      const prompt = this.chatbotUtilsService.buildPrompt(
        selectedPromptTemplate,
        user,
        mergedHealthContext,
        chatHistory,
        question,
        usermenuData,
        attachmentPrompt,
      );

      const systemPrompt =
        this.buildMessagePromptService.buildStrictSystemMessage();
      const llmMessages = this.chatbotUtilsService.buildLLMMessages(
        prompt,
        systemPrompt,
      );

      if (reqBody.attachment?.type === 'image' && reqBody.attachment.url) {
        const imageUrl = reqBody.attachment.url;

        // Modify last user message to include image
        const lastMessage = llmMessages.find((m) => m.role === 'user');
        if (lastMessage && typeof lastMessage.content === 'string') {
          (lastMessage as any).content = [
            {
              type: 'text',
              text: lastMessage.content,
            },
            {
              type: 'image_url',
              image_url: { url: imageUrl },
            },
          ];
        }
      }
      console.dir(llmMessages, { depth: null });

      // const response = await this.llm.invoke(llmMessages);
      let response;
      try {
        this.logger.log('Invoking LLM with messages...');
        response = await this.chatbotUtilsService.createLLM(this.openApiKey);
        this.logger.debug('LLM Raw Response:', response);
      } catch (error: any) {
        if (
          error?.status === 401 ||
          error?.message?.includes('Invalid API key')
        ) {
          this.logger.error(
            'OpenAI API key is invalid or expired. Action required.',
          );
          // Optional: send alert or flag system status
        } else if (error?.status === 429) {
          this.logger.warn(
            'OpenAI rate limit exceeded. Throttling or quota breach.',
          );
        } else if (error?.status === 500) {
          this.logger.error('OpenAI internal error.');
        } else {
          this.logger.error(
            `Unhandled OpenAI error: ${error?.message || error}`,
          );
        }
        throw new InternalServerErrorException(
          'Failed to get response please try again later',
        );
      }

      const userMessage = await this.messageModel.create({
        role: MESSAGE_ROLE.USER,
        content: question,
        sessionId: existing_chat_session?._id,
      });

      await this.messageModel.create({
        role: MESSAGE_ROLE.ASSISTANT,
        content: response.content,
        sessionId: existing_chat_session?._id,
        questionId: userMessage._id,
      });

      //Update session messages cache
      const updatedMessages = await this.messageModel
        .find({ sessionId: existing_chat_session?._id })
        .sort({ createdAt: 1 })
        .lean();

      const formattedMessages = updatedMessages.map(ChatMessageDTO.transform);

      const messageTimeline = [...formattedMessages];

      if (attachment?.url) {
        // Insert the attachment as a new message just before bot reply
        messageTimeline.push({
          _id: existing_chat_session?._id?.toString() || '',
          role: MESSAGE_ROLE.USER,
          content: userMessage.content,
          attachment: {
            type: attachment.type,
            url: attachment.url,
          },
          timestamp: new Date(),
        });
      }

      await this.chatbotRedisService.setCachedSessionMessages(
        user._id,
        existing_chat_session?._id?.toString() || '',
        messageTimeline,
      );

      const cleanReply = this.stripMarkdownBold(response.content);
      console.log('CleanReply:', cleanReply);

      const resp = ChatBotResponseDTO.transform({
        sessionId: existing_chat_session?._id,
        reply: cleanReply,
      });
      console.log('Final Response DTO:', resp);
      return {
        error: false,
        statusCode: HttpStatus.OK,
        data: resp,
      };
    } catch (error) {
      return {
        error: true,
        statusCode: HttpStatus.SERVICE_UNAVAILABLE,
        message: 'Something went wrong. Please try again later.',
        data: null,
      };
    }
  }

  async getAllSessionMessages(
    sessionId: string,
    queryFilters: GetAllSessionMessagesQueryInterface,
  ): Promise<GetAllSessionMessagesResDTO> {
    if (!Types.ObjectId.isValid(sessionId)) {
      throw new BadRequestException('Invalid Session Id Provided !!');
    }

    const { page, userId } = queryFilters;

    if (!userId) {
      throw new InternalServerErrorException('Please provide user Id !!');
    }

    // Check Redis first
    const cachedMessages =
      await this.chatbotRedisService.getCachedSessionMessages(
        userId,
        sessionId,
      );

    if (cachedMessages) {
      return {
        error: false,
        statusCode: HttpStatus.OK,
        data: {
          sessionId,
          messages: cachedMessages,
        },
      };
    }
    const { limit, offset } =
      this.utilsService.parsePageNumberAndGetlimitAndOffset(page);

    const existing_chat_session = await this.sessionModel.findOne({
      _id: sessionId,
      userId,
    });

    if (!existing_chat_session) {
      throw new BadRequestException('Session Not Found !!');
    }

    const messages = await this.messageModel
      .find({ sessionId: existing_chat_session._id })
      .sort({ createdAt: 1 }) // oldest first
      .limit(limit)
      .skip(offset)
      .lean();

    const formattedMessages = messages.map(ChatMessageDTO.transform);
    console.log('formattedMessages', formattedMessages);

    await this.chatbotRedisService.setCachedSessionMessages(
      userId,
      sessionId,
      formattedMessages,
    );

    return {
      error: false,
      statusCode: HttpStatus.OK,
      data: {
        sessionId: existing_chat_session._id
          ? existing_chat_session._id.toString()
          : '',
        messages: formattedMessages,
      },
    };
  }

  async classifyPromptUsingLLM(question: string): Promise<ChatbotCategory> {
    const prompt = CLASSIFICATION_PROMPT.replace('{question}', question);

    const systemPrompt = `You are a helpful assistant that only responds with one of the allowed category labels.`;
    const messages = [
      new SystemMessage(systemPrompt),
      new HumanMessage(prompt),
    ];

    const chatbotLLM = this.chatbotUtilsService.createLLM(
      this.openApiKey,
      'gpt-4o',
    );

    if (!chatbotLLM) {
      throw new InternalServerErrorException(
        'FSomething went wrong, please try again later',
      );
    }

    const res = await chatbotLLM.invoke(messages);

    const rawCategory = res.content?.toString().trim().toLowerCase();

    if (!rawCategory) {
      console.warn('Received empty or invalid category from LLM');
      return 'health';
    }

    const matched = SUPPORTED_CATEGORIES.find(
      (cat) => cat.toLowerCase() === rawCategory,
    );

    if (!matched) {
      console.warn('Unexpected category:', rawCategory);
      return 'health'; // fallback
    }

    return matched;
  }
}
