import { Injectable } from '@nestjs/common';

import { UserDTO } from './dto';
import { ConfigService } from '@nestjs/config';
import { Logger } from '@nestjs/common';
import { ChatOpenAI } from '@langchain/openai';

@Injectable()
export class ChatbotUtilsService {
  constructor(
    private readonly logger: Logger,
    private readonly configService: ConfigService,
  ) {
    const openaiKey = this.configService.get<string>('OPENAI_API_KEY');

    if (!openaiKey) {
      this.logger.error(
        'OPENAI_API_KEY is missing. ChatOpenAI cannot be initialized.',
      );
      throw new Error('Missing OpenAI API Key');
    }
  }

  createLLM(apiKey: string, model = 'gpt-4o'): ChatOpenAI | null {
    if (!apiKey) {
      this.logger.error('OPENAI_API_KEY is missing.');
      return null;
    }

    try {
      const llm = new ChatOpenAI({
        model,
        temperature: 0,
        apiKey,
      });
      this.logger.log(
        `<PERSON>t<PERSON>penA<PERSON> initialized successfully with model '${model}'.`,
      );
      return llm;
    } catch (error) {
      this.logger.error(
        'Failed to initialize ChatOpenAI.',
        error instanceof Error ? error.stack : String(error),
      );
      return null;
    }
  }
  stripMarkdownBold(text: string): string {
    return text.replace(/\*\*(.*?)\*\*/g, '$1');
  }

  resolveRelativeDates(question: string): string {
    const now = new Date();
    const today = new Date(now);
    const yesterday = new Date(now);
    yesterday.setDate(today.getDate() - 1);

    const format = (date: Date) => date.toISOString().split('T')[0];

    return question
      .replace(/\btoday\b/gi, format(today))
      .replace(/\byesterday\b/gi, format(yesterday));
  }

  buildPrompt(
    template: string,
    user: UserDTO,
    healthData: any,
    chatHistory: any[],
    question: string,
    userManuData: any,
    attachmentPrompt: string = '',
  ): string {
    const serializedDump = healthData?.dump
      ? {
          manualData: healthData.dump.manualData?.slice(0, 10) ?? [],
          systemData: healthData.dump.systemData?.slice(0, 10) ?? [],
          ledgerData: healthData.dump.ledgerData?.slice(0, 10) ?? [],
        }
      : {};

    const serializedMicroservice = healthData?.microservice?.data
      ? healthData.microservice.data.map((entry) => ({
          name: entry.name,
          measurements: entry.measurements,
          time: entry.time,
        }))
      : [];

    const serializedManuData = {
      deviceUsage: userManuData?.userDeviceRecordData?.slice(-5) || [],
      moodRecords: userManuData?.userMoodRecordData?.slice(-5) || [],
      mealRecords: userManuData?.userMealRecordData?.slice(-5) || [],
      weightSummary: userManuData?.monthlyWeightSummaryData?.slice(-3) || [],
      sleepRecords: userManuData?.userSleepRecords?.slice(-5) || [],
    };

    const currentDate = new Date().toLocaleDateString('en-GB', {
      day: '2-digit',
      month: 'long',
      year: 'numeric',
    });

    return template
      .replace('${user.age}', String(user.age ?? 'N/A'))
      .replace('${user.height}', String(user.height ?? 'N/A'))
      .replace('${user.weight}', String(user.weight ?? 'N/A'))
      .replace(
        '${JSON.stringify(user.goals)}',
        JSON.stringify(user.goals ?? []),
      )
      .replace(
        '${JSON.stringify(healthData.microservice)}',
        JSON.stringify(serializedMicroservice),
      )
      .replace(
        '${JSON.stringify(healthData.dump)}',
        JSON.stringify(serializedDump),
      )
      .replace('${attachmentPrompt}', attachmentPrompt)
      .replace(
        '${mealRecords}',
        JSON.stringify(serializedManuData.mealRecords ?? []),
      )
      .replace(
        '${moodRecords}',
        JSON.stringify(serializedManuData.moodRecords ?? []),
      )
      .replace(
        '${sleepRecords}',
        JSON.stringify(serializedManuData.sleepRecords ?? []),
      )
      .replace(
        '${deviceUsage}',
        JSON.stringify(serializedManuData.deviceUsage ?? []),
      )
      .replace(
        '${weightSummary}',
        JSON.stringify(serializedManuData.weightSummary ?? []),
      )
      .replace(
        '${JSON.stringify(chatHistory)}',
        JSON.stringify(chatHistory ?? []),
      )
      .replace('${question}', question)
      .replace('${currentDate}', currentDate);
  }

  buildLLMMessages(prompt: string, systemPrompt: string) {
    return [
      { role: 'system', content: systemPrompt },
      { role: 'user', content: prompt },
    ];
  }
}
