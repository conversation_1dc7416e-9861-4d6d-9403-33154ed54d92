import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { CustomLogger } from './common/services';
import { CorsOptions } from '@nestjs/common/interfaces/external/cors-options.interface';
import { RuntimeExceptionFilter } from './middlewares';
import { NestExpressApplication } from '@nestjs/platform-express';
import { Response } from 'express';
import { ValidationPipe } from '@nestjs/common';
import * as os from 'os';

async function bootstrap() {
  // Inline function to get local IPv4 address
  function getServerIp(): string | null {
    const interfaces = os.networkInterfaces();
    for (const name of Object.keys(interfaces)) {
      for (const iface of interfaces[name] || []) {
        if (iface.family === 'IPv4' && !iface.internal) {
          return iface.address;
        }
      }
    }
    return null;
  }

  const app = await NestFactory.create<NestExpressApplication>(AppModule, {
    logger: ['log', 'error', 'debug', 'warn', 'verbose'],
  });

  app.setGlobalPrefix('api');

  const corsOptions: CorsOptions = {
    origin: process.env.MAIN_BACKEND_URL ? [process.env.MAIN_BACKEND_URL] : '',
    methods: 'GET,HEAD,PUT,PATCH,POST,DELETE,OPTIONS',
    credentials: true,
  };
  app.enableCors(corsOptions);

  app.useLogger(app.get(CustomLogger));

  app.useGlobalPipes(
    new ValidationPipe({
      transform: true,
      whitelist: true,
      forbidNonWhitelisted: true,
      validateCustomDecorators: true,
    }),
  );

  app.useGlobalFilters(new RuntimeExceptionFilter(app.get(CustomLogger)));

  const options = new DocumentBuilder()
    .setTitle('Appetec API Documentation')
    .setDescription('API for managing Appetec platform resources.')
    .setVersion('1.0')
    .addBearerAuth()
    .build();

  const document = SwaggerModule.createDocument(app, options);
  SwaggerModule.setup('/api/swagger', app, document);

  // Welcome message for root URL
  app.getHttpAdapter().get('/', (req, res: Response) => {
    res.status(200).send('Welcome to Appetec Chatbot Microservice');
  });

  const PORT = parseInt(process.env.SERVER_PORT || '3000', 10);
  const IP = getServerIp();

  if (IP) {
    await app.listen(PORT, IP);
    console.log(`\nChatBot Microservice is running on http://${IP}:${PORT}\n`);
  } else {
    await app.listen(PORT);
    console.log(
      `\nChatBot Microservice is running on http://localhost:${PORT}\n`,
    );
  }
}

bootstrap().catch((err) => {
  console.error('Failed to start application:', err);
  process.exit(1);
});
